import React, { Suspense } from 'react'
import _360NavbarComponent from '@/components/360s/_360NavbarComponent';
import _360Navbar from '@/components/360s/_360Navbar';
import PopupWrapper from '@/components/menu-popup/PopupWrapper';
import MenuPopupWrapper from '@/components/menu-popup/MenuPopupWrapper';

export default function layout({children}) {
  return (
    <div className='flex flex-grow w-full h-svh items-center justify-center overflow-hidden'>
      <Suspense fallback={null}>
        <_360Navbar/>
        <PopupWrapper/>
        <MenuPopupWrapper/>
      </Suspense>
      {children}
    </div>
  )
}
