export const INITIAL_EXPERIENCE_STATE = {
    showNavbar: false,
    showMenu: false,
    showPopup: false,
    showBookingPopup: false,
    showGalleryStore: false,
    showVideoGallery: false,
    showItemInfo: false,
    ref360Sphere: null,
    refControls: null,
    refGUI: null
};

export const ACTIONS_EXPERIENCE_STATE = {
    SHOW_NAVBAR: 'SHOW_NAVBAR',
    MENU_TOGGLE: 'MENU_TOGGLE',
    POPUP_TOGGLE: 'POPUP_TOGGLE',
    ATTACH_360_SPHERE: 'ATTACH_360_SPHERE',
    ATTACH_360_CONTROLS: 'ATTACH_360_CONTROLS',
    ADD_GUI: 'ADD_GUI',
};

export const experienceReducer = (state, action) => {
    switch (action.type) {
      case 'ADD_GUI':
        return { 
          ...state, 
          refGUI: action.payload 
        };
      case 'SHOW_NAVBAR':
        return { 
          ...state, 
          showNavbar: !state.showNavbar 
        };
      case 'MENU_TOGGLE':
        return { 
          ...state, 
          showMenu: !state.showMenu 
        };
      case 'POPUP_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup  
        };
      case 'ATTACH_360_SPHERE':
        return { 
          ...state, 
          ref360Sphere: action.payload,    // Update the state with the new ref360Sphere value
        };
      case 'ATTACH_360_CONTROLS':
        return { 
          ...state, 
          refControls: action.payload,    // Update the state with the new refControls value
        };
      default:
        return state;
    }
  };