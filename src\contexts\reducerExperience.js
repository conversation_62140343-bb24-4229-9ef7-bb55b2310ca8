export const INITIAL_EXPERIENCE_STATE = {
    showNavbar: false,
    showMenu: false,
    showPopup: false,
    showBookingPopup: false,
    showGalleryStore: false,
    showVideoGallery: false,
    showItemInfo: false,
};

export const ACTIONS_EXPERIENCE_STATE = {
    SHOW_NAVBAR: 'SHOW_NAVBAR',
    MENU_TOGGLE: 'MENU_TOGGLE',
    POPUP_TOGGLE: 'POPUP_TOGGLE',
    POPUP_BOOKING_TOGGLE: 'POPUP_BOOKING_TOGGLE',
    POPUP_STORE_TOGGLE: 'POPUP_STORE_TOGGLE',
    POPUP_VIDOE_GALLERY_TOGGLE: 'POPUP_VIDOE_GALLERY_TOGGLE',
    POPUP_ITEM_GALLERY_TOGGLE: 'POPUP_ITEM_GALLERY_TOGGLE',
};

export const experienceReducer = (state, action) => {
    switch (action.type) {
      case 'SHOW_NAVBAR':
        return { 
          ...state, 
          showNavbar: !state.showNavbar 
        };
      case 'MENU_TOGGLE':
        return { 
          ...state, 
          showMenu: !state.showMenu 
        };
      case 'POPUP_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup  
        };
      case 'POPUP_BOOKING_TOGGLE':
        return { 
          ...state, 
           showPopup: !state.showPopup,
          showBookingPopup: true,
          showGalleryStore: false,
          showVideoGallery: false,
          showItemInfo: false,  
        };
      case 'POPUP_STORE_TOGGLE':
        return { 
          ...state, 
           showPopup: !state.showPopup,
          showGalleryStore: true,
          showBookingPopup: false,
          showVideoGallery: false,
          showItemInfo: false,    
        };
      case 'POPUP_VIDOE_GALLERY_TOGGLE':
        return { 
          ...state, 
           showPopup: !state.showPopup,
          showVideoGallery: true,
          showBookingPopup: false,
          showGalleryStore: false,
          showItemInfo: false,    
        };
      case 'POPUP_ITEM_GALLERY_TOGGLE':
        return { 
          ...state, 
           showPopup: !state.showPopup,
          showItemInfo: true,
          showBookingPopup: false,
          showGalleryStore: false,
          showVideoGallery: false,    
        };
      default:
        return state;
    }
  };