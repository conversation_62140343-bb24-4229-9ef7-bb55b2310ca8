import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';

// GET /api/bookings/availability - Check date availability
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    if (!startDate || !endDate) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'startDate and endDate are required',
        },
        { status: 400 }
      );
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start >= end) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'startDate must be before endDate',
        },
        { status: 400 }
      );
    }
    
    // Find all bookings that overlap with the requested date range
    const overlappingBookings = await Booking.find({
      status: { $in: ['pending', 'confirmed', 'checked_in'] },
      $or: [
        {
          // Booking starts within the requested range
          'dates.checkIn': {
            $gte: start,
            $lt: end,
          },
        },
        {
          // Booking ends within the requested range
          'dates.checkOut': {
            $gt: start,
            $lte: end,
          },
        },
        {
          // Booking spans the entire requested range
          'dates.checkIn': { $lte: start },
          'dates.checkOut': { $gte: end },
        },
      ],
    }).populate('customer', 'firstname surname name email')
     .populate('package', 'name category');
    
    // Generate array of all booked dates
    const bookedDates = [];
    overlappingBookings.forEach(booking => {
      if (booking.dates && booking.dates.checkIn && booking.dates.checkOut) {
        const checkIn = new Date(booking.dates.checkIn);
        const checkOut = new Date(booking.dates.checkOut);
        
        // Add all dates between check-in and check-out (exclusive of check-out)
        const currentDate = new Date(checkIn);
        while (currentDate < checkOut) {
          bookedDates.push({
            date: new Date(currentDate).toISOString().split('T')[0],
            bookingId: booking._id,
            customer: booking.customer,
            package: booking.package,
            status: booking.status,
          });
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }
    });
    
    // Check if the requested range is available
    const requestedDates = [];
    const currentDate = new Date(start);
    while (currentDate < end) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const isBooked = bookedDates.some(bookedDate => bookedDate.date === dateStr);
      
      requestedDates.push({
        date: dateStr,
        available: !isBooked,
        booking: isBooked ? bookedDates.find(bd => bd.date === dateStr) : null,
      });
      
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    const isRangeAvailable = requestedDates.every(date => date.available);
    
    return NextResponse.json({
      success: true,
      data: {
        startDate: start.toISOString().split('T')[0],
        endDate: end.toISOString().split('T')[0],
        isAvailable: isRangeAvailable,
        requestedDates,
        bookedDates: bookedDates.map(bd => bd.date),
        conflictingBookings: overlappingBookings.map(booking => ({
          id: booking._id,
          bookingNumber: booking.bookingNumber,
          customer: booking.customer,
          package: booking.package,
          checkIn: booking.dates.checkIn,
          checkOut: booking.dates.checkOut,
          status: booking.status,
        })),
      },
    });
  } catch (error) {
    console.error('Error checking availability:', error);

    // Provide more specific error information
    let errorMessage = error.message;
    if (error.name === 'MongooseError' || error.name === 'MongoError') {
      errorMessage = 'Database connection error. Please try again later.';
    } else if (error.name === 'ValidationError') {
      errorMessage = 'Invalid data format. Please check your request.';
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check availability',
        message: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}

// POST /api/bookings/availability - Bulk check multiple date ranges
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { dateRanges } = body;
    
    if (!Array.isArray(dateRanges)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'dateRanges must be an array',
        },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const range of dateRanges) {
      const { startDate, endDate } = range;
      
      if (!startDate || !endDate) {
        results.push({
          startDate,
          endDate,
          error: 'startDate and endDate are required',
        });
        continue;
      }
      
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start >= end) {
        results.push({
          startDate,
          endDate,
          error: 'startDate must be before endDate',
        });
        continue;
      }
      
      // Check availability for this range
      const overlappingBookings = await Booking.find({
        status: { $in: ['pending', 'confirmed', 'checked_in'] },
        $or: [
          {
            'dates.checkIn': { $gte: start, $lt: end },
          },
          {
            'dates.checkOut': { $gt: start, $lte: end },
          },
          {
            'dates.checkIn': { $lte: start },
            'dates.checkOut': { $gte: end },
          },
        ],
      });
      
      const isAvailable = overlappingBookings.length === 0;
      
      results.push({
        startDate: start.toISOString().split('T')[0],
        endDate: end.toISOString().split('T')[0],
        isAvailable,
        conflictingBookings: overlappingBookings.length,
      });
    }
    
    return NextResponse.json({
      success: true,
      data: results,
    });
  } catch (error) {
    console.error('Error bulk checking availability:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check availability',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
