'use client';

import { Inter } from 'next/font/google';
import './globals.css';
import Link from 'next/link';
import { ExperienceContextProvider } from '@/contexts/useContextExperience';
import PopupWrapper from '@/components/menu-popup/PopupWrapper';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({ children }) {
  const link=['admin/dashboard','beta'];
  return (
    <html lang="en">
      <body className={`${inter.className} bg-black antialiased`}>
        <div className="site-links flex absolute w-fit h-fit flex-col-reverse items-end z-10 bottom-0 right-0 p-4 text-teal-500 underline-offset-1 capitalize gap-2">
          {link.map((i,index) =>
            <Link key={index} href={`/${i}`}>{i}</Link>
          )}
        </div>
        <ExperienceContextProvider>
          {children}
          <PopupWrapper/>
        </ExperienceContextProvider>
      </body>
    </html>
  );
}
